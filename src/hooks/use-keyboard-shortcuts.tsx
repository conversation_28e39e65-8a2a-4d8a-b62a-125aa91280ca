import React, { createContext, useContext, useEffect, useState } from 'react';
import { useApp } from '@/lib/app-context';

// Define shortcut categories
export type ShortcutCategory =
  | 'navigation'
  | 'messaging'
  | 'views'
  | 'general'
  | 'threads'
  | 'topics';

// Define a shortcut
export interface Shortcut {
  id: string;
  keys: string[];
  description: string;
  category: ShortcutCategory;
  isEnabled: () => boolean;
  action: (e: KeyboardEvent) => void;
}

interface KeyboardShortcutsContextType {
  shortcuts: Shortcut[];
  selectedMessageId: string | null;
  setSelectedMessageId: (id: string | null) => void;
  isKeyboardNavigationModeEnabled: boolean;
}

const KeyboardShortcutsContext = createContext<KeyboardShortcutsContextType | undefined>(undefined);

export const KeyboardShortcutsProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [selectedMessageId, setSelectedMessageId] = useState<string | null>(null);
  const {
    currentChannel,
    currentDirectMessage,
    currentThread,
    setActiveThread,
    toggleSidebar,
    isSidebarOpen,
    setCurrentChannelActiveView,
    currentChannelActiveView,
    setIsSearchViewActive,
    workspaceSettings,
    getCurrentUser,
    sendMessage,
    navigateBack,
    navigateForward,
    canGoBack,
    canGoForward,
    workspace,
  } = useApp();

  // Check if keyboard navigation mode is enabled (default: true)
  const currentUser = getCurrentUser();
  const keyboardNavigationModeOverride = currentUser?.settings?.keyboardNavigationModeOverride;
  const isKeyboardNavigationModeEnabled =
    keyboardNavigationModeOverride !== null && keyboardNavigationModeOverride !== undefined
      ? keyboardNavigationModeOverride
      : workspaceSettings?.keyboardNavigationMode ?? true;

  // Define all shortcuts
  const shortcuts: Shortcut[] = [
    // Navigation shortcuts
    {
      id: 'toggle-sidebar',
      keys: ['Ctrl+Shift+D', '⌘+Shift+D'],
      description: 'Toggle sidebar',
      category: 'navigation',
      isEnabled: () => true,
      action: (e) => {
        e.preventDefault();
        toggleSidebar();
      }
    },
    {
      id: 'navigate-back',
      keys: ['Alt+ArrowLeft', 'Ctrl+[', '⌘+['],
      description: 'Go back in history',
      category: 'navigation',
      isEnabled: () => canGoBack,
      action: (e) => {
        e.preventDefault();
        navigateBack();
      }
    },
    {
      id: 'navigate-forward',
      keys: ['Alt+ArrowRight', 'Ctrl+]', '⌘+]'],
      description: 'Go forward in history',
      category: 'navigation',
      isEnabled: () => canGoForward,
      action: (e) => {
        e.preventDefault();
        navigateForward();
      }
    },
    {
      id: 'show-history',
      keys: ['H'],
      description: 'Show history dropdown',
      category: 'navigation',
      isEnabled: () => isKeyboardNavigationModeEnabled,
      action: (e) => {
        // Check if search input is focused
        const isSearchFocused = document.activeElement?.classList.contains('search-input');
        if (isSearchFocused) {
          return; // Don't prevent default if search is focused
        }

        // This is handled in TopBar component
        e.preventDefault();
      }
    },
    {
      id: 'command-palette',
      keys: ['Ctrl+K', '⌘+K'],
      description: 'Open command palette',
      category: 'navigation',
      isEnabled: () => true,
      action: (e) => {
        e.preventDefault();
        // This is handled in AppLayout, but we need to prevent default
      }
    },
    {
      id: 'workspace-switch',
      keys: ['Alt+W'],
      description: 'Switch workspace',
      category: 'navigation',
      isEnabled: () => true,
      action: (e) => {
        e.preventDefault();
        // This is handled in AppLayout, but we need to prevent default
      }
    },
    {
      id: 'search',
      keys: ['Ctrl+Shift+F', '⌘+Shift+F', '/'], // Added / as a shortcut
      description: 'Focus search input',
      category: 'navigation',
      isEnabled: () => true,
      action: (e) => {
        // Don't handle Ctrl+/ as it's reserved for keyboard shortcuts
        if (e.key === '/' && (e.ctrlKey || e.metaKey)) {
          return;
        }

        e.preventDefault();

        // Close any open history popover that might interfere with search focus
        const historyPopover = document.querySelector('[role="dialog"]');
        if (historyPopover) {
          // Find and click the close button if it exists
          const closeButton = historyPopover.querySelector('button[aria-label="Close"]');
          if (closeButton) {
            (closeButton as HTMLButtonElement).click();
          }
        }

        // Focus the search input if it exists
        const searchInput = document.querySelector('.search-input') as HTMLInputElement;
        if (searchInput) {
          // Use setTimeout to ensure the focus happens after any other operations
          setTimeout(() => {
            // Ensure any click handlers on the search input are triggered
            searchInput.click();
            searchInput.focus();

            // Dispatch a focus event to ensure all focus handlers are triggered
            const focusEvent = new FocusEvent('focus', { bubbles: true });
            searchInput.dispatchEvent(focusEvent);
          }, 0);
        }
      }
    },
    {
      id: 'search-history-up',
      keys: ['ArrowUp'],
      description: 'Navigate to previous search in history',
      category: 'navigation',
      isEnabled: () => document.activeElement?.classList.contains('search-input'),
      action: (_e) => {
        // This is handled in the TopBar component
      }
    },
    {
      id: 'search-history-down',
      keys: ['ArrowDown'],
      description: 'Navigate to next search in history',
      category: 'navigation',
      isEnabled: () => document.activeElement?.classList.contains('search-input'),
      action: (_e) => {
        // This is handled in the TopBar component
      }
    },

    // Messaging shortcuts
    {
      id: 'new-message',
      keys: ['N'], // Simple N key for new message
      description: 'New message',
      category: 'messaging',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView !== 'Topics',
      action: (e) => {
        if (isKeyboardNavigationModeEnabled) {
          e.preventDefault();
          // Open new message dialog
          const newMessageButton = document.querySelector('button[title="New Message"]');
          if (newMessageButton) {
            (newMessageButton as HTMLButtonElement).click();
          }
        }
      }
    },
    {
      id: 'reply-to-message',
      keys: ['R'], // Simple R key for reply
      description: 'Reply to selected message',
      category: 'messaging',
      isEnabled: () => isKeyboardNavigationModeEnabled && !!selectedMessageId && !currentThread,
      action: (e) => {
        if (isKeyboardNavigationModeEnabled && selectedMessageId && !currentThread) {
          e.preventDefault();
          setActiveThread(selectedMessageId);
        }
      }
    },
    {
      id: 'focus-message-input',
      keys: ['M'],
      description: 'Focus message input',
      category: 'messaging',
      isEnabled: () => isKeyboardNavigationModeEnabled && (!!currentChannel || !!currentDirectMessage),
      action: (e) => {
        e.preventDefault();
        const messageInput = document.querySelector('textarea.message-input') as HTMLTextAreaElement;
        if (messageInput) {
          messageInput.focus();
        }
      }
    },

    // Close thread shortcut (moved to messaging category)
    {
      id: 'close-thread',
      keys: ['Escape'],
      description: 'Close thread',
      category: 'messaging',
      isEnabled: () => !!currentThread,
      action: (e) => {
        if (currentThread) {
          e.preventDefault();
          setActiveThread(null);
        }
      }
    },

    // Exit search view shortcut
    {
      id: 'exit-search',
      keys: ['Escape'],
      description: 'Exit search view',
      category: 'navigation',
      isEnabled: () => true,
      action: (e) => {
        // Check if we're in search view
        const isInSearchView = document.querySelector('.app-main-content')?.textContent?.includes('Search Results');
        if (isInSearchView) {
          e.preventDefault();
          setIsSearchViewActive(false);
        }
      }
    },

    // View shortcuts
    {
      id: 'view-messages',
      keys: ['Alt+1'],
      description: 'Switch to Messages view',
      category: 'views',
      isEnabled: () => {
        if (!currentChannel) return false;
        // Check if this view is available in the channel
        const availableViews = currentChannel.views ||
                              currentChannel.channelSpecificDefaultViews ||
                              workspaceSettings?.defaultChannelViews ||
                              ['Messages', 'Topics', 'Files', 'Members', 'Note'];
        return availableViews.includes('Messages');
      },
      action: (e) => {
        if (currentChannel) {
          e.preventDefault();
          setCurrentChannelActiveView('Messages');
        }
      }
    },
    {
      id: 'view-topics',
      keys: ['Alt+2'],
      description: 'Switch to Topics view',
      category: 'views',
      isEnabled: () => {
        if (!currentChannel) return false;
        // Check if this view is available in the channel
        const availableViews = currentChannel.views ||
                              currentChannel.channelSpecificDefaultViews ||
                              workspaceSettings?.defaultChannelViews ||
                              ['Messages', 'Topics', 'Files', 'Members', 'Note'];
        return availableViews.includes('Topics');
      },
      action: (e) => {
        if (currentChannel) {
          e.preventDefault();
          setCurrentChannelActiveView('Topics');
        }
      }
    },
    {
      id: 'view-files',
      keys: ['Alt+3'],
      description: 'Switch to Files view',
      category: 'views',
      isEnabled: () => {
        if (!currentChannel) return false;
        // Check if this view is available in the channel
        const availableViews = currentChannel.views ||
                              currentChannel.channelSpecificDefaultViews ||
                              workspaceSettings?.defaultChannelViews ||
                              ['Messages', 'Topics', 'Files', 'Members', 'Note'];
        return availableViews.includes('Files');
      },
      action: (e) => {
        if (currentChannel) {
          e.preventDefault();
          setCurrentChannelActiveView('Files');
        }
      }
    },
    {
      id: 'view-members',
      keys: ['Alt+4'],
      description: 'Switch to Members view',
      category: 'views',
      isEnabled: () => {
        if (!currentChannel) return false;
        // Check if this view is available in the channel
        const availableViews = currentChannel.views ||
                              currentChannel.channelSpecificDefaultViews ||
                              workspaceSettings?.defaultChannelViews ||
                              ['Messages', 'Topics', 'Files', 'Members', 'Note'];
        return availableViews.includes('Members');
      },
      action: (e) => {
        if (currentChannel) {
          e.preventDefault();
          setCurrentChannelActiveView('Members');
        }
      }
    },
    {
      id: 'view-note',
      keys: ['Alt+5'],
      description: 'Switch to Note view',
      category: 'views',
      isEnabled: () => {
        if (!currentChannel) return false;
        // Check if this view is available in the channel
        const availableViews = currentChannel.views ||
                              currentChannel.channelSpecificDefaultViews ||
                              workspaceSettings?.defaultChannelViews ||
                              ['Messages', 'Topics', 'Files', 'Members', 'Note'];
        return availableViews.includes('Note');
      },
      action: (e) => {
        if (currentChannel) {
          e.preventDefault();
          setCurrentChannelActiveView('Note');
        }
      }
    },

    // General shortcuts
    {
      id: 'keyboard-shortcuts',
      keys: ['Shift+?', 'Ctrl+/', '⌘+/'], // Added back Ctrl+/ and ⌘+/
      description: 'Show keyboard shortcuts',
      category: 'general',
      isEnabled: () => true,
      action: (e) => {
        e.preventDefault();
        // This is handled in AppLayout
      }
    },
    {
      id: 'user-preferences',
      keys: ['Ctrl+.', '⌘+.'], // Changed to Ctrl+. which is commonly used for settings in many apps
      description: 'User preferences',
      category: 'general',
      isEnabled: () => isKeyboardNavigationModeEnabled,
      action: (e) => {
        if (isKeyboardNavigationModeEnabled) {
          e.preventDefault();

          // Direct approach to open user preferences dialog
          // Access the component's state setter directly
          if ((window as any).__APP_COMPONENT__) {
            const app = (window as any).__APP_COMPONENT__;
            if (typeof app.setIsUserPreferencesOpen === 'function') {
              app.setIsUserPreferencesOpen(true);
              return;
            }
          }

          // Fallback: Try to find the preferences menu item in the DOM
          // First try to find the user section
          const userSection = document.querySelector('.user-section');
          if (userSection) {
            // Click on the user section to open the dropdown
            const userTrigger = userSection.querySelector('.dropdown-trigger, [role="button"]');
            if (userTrigger) {
              (userTrigger as HTMLElement).click();

              // Wait for the dropdown to open
              setTimeout(() => {
                // Look for the preferences option
                const menuItems = document.querySelectorAll('[role="menuitem"]');
                for (const item of menuItems) {
                  if (item.textContent?.includes('Preferences')) {
                    (item as HTMLElement).click();
                    break;
                  }
                }
              }, 100);
            }
          }
        }
      }
    },
    {
      id: 'workspace-settings',
      keys: ['Ctrl+Alt+S', '⌘+Alt+S'], // Changed to Ctrl+Alt+S / ⌘+Alt+S to avoid conflict with user preferences
      description: 'Workspace settings',
      category: 'general',
      isEnabled: () => {
        if (!isKeyboardNavigationModeEnabled) return false;

        // Check if current user is admin
        const currentUser = workspace?.users.find(u => u.id === workspace?.currentUserId);
        return currentUser?.workspaceRole === 'admin';
      },
      action: (e) => {
        if (isKeyboardNavigationModeEnabled) {
          e.preventDefault();

          // Check admin privileges before opening
          const currentUser = workspace?.users.find(u => u.id === workspace?.currentUserId);
          if (currentUser?.workspaceRole !== 'admin') {
            // Import toast from sonner for error feedback
            const { toast } = require('sonner');
            toast.error('Access denied: Only workspace administrators can access workspace settings.');
            return;
          }

          // Direct approach to open workspace settings dialog
          // Access the component's state setter directly
          if ((window as any).__APP_COMPONENT__) {
            const app = (window as any).__APP_COMPONENT__;
            if (typeof app.setIsWorkspaceSettingsDialogOpen === 'function') {
              app.setIsWorkspaceSettingsDialogOpen(true);
              return;
            }
          }

          // Fallback: Try to find the workspace settings menu item in the DOM
          // First try to find the workspace dropdown trigger
          const workspaceTrigger = document.querySelector('[data-workspace-menu]');
          if (workspaceTrigger) {
            // Click on the workspace trigger to open the dropdown
            (workspaceTrigger as HTMLElement).click();

            // Wait for the dropdown to open
            setTimeout(() => {
              // Look for the workspace settings option
              const settingsOption = document.querySelector('[data-workspace-settings]');
              if (settingsOption) {
                (settingsOption as HTMLElement).click();
              } else {
                // Try to find by content
                const menuItems = document.querySelectorAll('[role="menuitem"]');
                for (const item of menuItems) {
                  if (item.textContent?.includes('Workspace Settings')) {
                    (item as HTMLElement).click();
                    break;
                  }
                }
              }
            }, 100);
          }
        }
      }
    },
    {
      id: 'add-channel',
      keys: ['Ctrl+Shift+L', '⌘+Shift+L'], // Changed to L for "channel" to avoid conflict with N
      description: 'Add channel',
      category: 'general',
      isEnabled: () => isKeyboardNavigationModeEnabled,
      action: (e) => {
        if (isKeyboardNavigationModeEnabled) {
          e.preventDefault();
          // Find and click the add channel button
          const addChannelButton = document.querySelector('[data-add-channel]') as HTMLButtonElement;
          if (addChannelButton) {
            addChannelButton.click();
          }
        }
      }
    },
    {
      id: 'add-project',
      keys: ['Ctrl+Shift+G', '⌘+Shift+G'], // G for Group/Project (avoiding P which might be used elsewhere)
      description: 'Add project/section',
      category: 'general',
      isEnabled: () => isKeyboardNavigationModeEnabled,
      action: (e) => {
        if (isKeyboardNavigationModeEnabled) {
          e.preventDefault();
          // Directly set the dialog open state
          const setIsAddSectionDialogOpen = (open: boolean) => {
            // Find the dialog by its title
            const addSectionDialog = document.querySelector('div[role="dialog"] h2');
            if (addSectionDialog && addSectionDialog.textContent?.includes('Create a new section')) {
              // Dialog is already open, find and click the close button if we want to close it
              if (!open) {
                const closeButton = addSectionDialog.closest('div[role="dialog"]')?.querySelector('button[aria-label="Close"]');
                if (closeButton) {
                  (closeButton as HTMLButtonElement).click();
                }
              }
            } else if (open) {
              // Need to open the dialog - look for a button that might trigger it
              // This is a direct approach to open the Add Section dialog
              const sidebarElement = document.querySelector('.app-sidebar');
              if (sidebarElement) {
                // Create and dispatch a custom event that the Sidebar component can listen for
                const event = new CustomEvent('openAddSectionDialog');
                sidebarElement.dispatchEvent(event);

                // As a fallback, try to programmatically set the state
                // This is a bit of a hack but might work in some cases
                const sidebarComponent = (window as any).__SIDEBAR_COMPONENT__;
                if (sidebarComponent && typeof sidebarComponent.setIsAddSectionDialogOpen === 'function') {
                  sidebarComponent.setIsAddSectionDialogOpen(true);
                }
              }
            }
          };

          // Try to open the Add Section dialog
          setIsAddSectionDialogOpen(true);
        }
      }
    },

    // Topic navigation shortcuts
    {
      id: 'new-topic',
      keys: ['N'],
      description: 'Create new topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: (e) => {
        if (isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics') {
          e.preventDefault();
          // Find and click the new topic button
          const newTopicButton = document.querySelector('button[title="New topic (N)"]') as HTMLButtonElement;
          if (newTopicButton) {
            newTopicButton.click();
          }
        }
      }
    },
    {
      id: 'back-to-topics',
      keys: ['Escape'],
      description: 'Back to topics list',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
    {
      id: 'navigate-topic-up',
      keys: ['j', 'ArrowUp'],
      description: 'Navigate to previous topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
    {
      id: 'navigate-topic-down',
      keys: ['k', 'ArrowDown'],
      description: 'Navigate to next topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
    {
      id: 'open-topic',
      keys: ['Enter'],
      description: 'Open selected topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
    {
      id: 'first-topic',
      keys: ['Home'],
      description: 'Go to first topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
    {
      id: 'last-topic',
      keys: ['End'],
      description: 'Go to last topic',
      category: 'topics',
      isEnabled: () => isKeyboardNavigationModeEnabled && currentChannelActiveView === 'Topics',
      action: () => {
        // This is handled in the ChannelTopics component
      }
    },
  ];

  // Set up global keyboard event listener
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // For shortcuts that should work even when typing
      const isInInputField =
        e.target instanceof HTMLInputElement ||
        e.target instanceof HTMLTextAreaElement ||
        (e.target as HTMLElement).isContentEditable;

      // Process shortcuts
      for (const shortcut of shortcuts) {
        if (shortcut.isEnabled()) {
          const matchesCtrlCmd = shortcut.keys.some(k =>
            k.includes('Ctrl+') && e.ctrlKey ||
            k.includes('⌘+') && e.metaKey
          );

          const matchesAlt = shortcut.keys.some(k =>
            k.includes('Alt+') && e.altKey
          );

          const matchesShift = shortcut.keys.some(k =>
            k.includes('Shift+') && e.shiftKey
          );

          const matchesKey = shortcut.keys.some(k => {
            const keyPart = k.split('+').pop() || '';
            return keyPart.toLowerCase() === (e.key || '').toLowerCase();
          });

          // Check if this shortcut should work in input fields
          const worksInInputField = matchesCtrlCmd || matchesAlt;

          // Skip shortcuts that shouldn't work in input fields when typing
          if (isInInputField && !worksInInputField) {
            continue;
          }

          if (
            matchesKey &&
            ((!matchesCtrlCmd && !matchesAlt && !matchesShift) ||
             (matchesCtrlCmd && (e.ctrlKey || e.metaKey)) ||
             (matchesAlt && e.altKey) ||
             (matchesShift && e.shiftKey))
          ) {
            shortcut.action(e);
            break;
          }
        }
      }
    };

    // Use capture phase to ensure we get events before other handlers
    window.addEventListener('keydown', handleKeyDown, true);
    return () => window.removeEventListener('keydown', handleKeyDown, true);
  }, [shortcuts, selectedMessageId, currentThread, currentChannel, currentChannelActiveView, workspace]);

  return (
    <KeyboardShortcutsContext.Provider
      value={{
        shortcuts,
        selectedMessageId,
        setSelectedMessageId,
        isKeyboardNavigationModeEnabled
      }}
    >
      {children}
    </KeyboardShortcutsContext.Provider>
  );
};

export const useKeyboardShortcuts = () => {
  const context = useContext(KeyboardShortcutsContext);
  if (context === undefined) {
    throw new Error('useKeyboardShortcuts must be used within a KeyboardShortcutsProvider');
  }
  return context;
};
