import { useState, useEffect, useRef, useMemo } from 'react';
import { Di<PERSON>, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { AtSign, Hash, Plus, X, Search, Paperclip, Lock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useApp } from '@/lib/app-context';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { ChannelTopic, Channel, User, LocalAttachment } from '@/lib/types';
import SimpleMDEEditor from 'react-simplemde-editor';
import 'easymde/dist/easymde.min.css';
import type { Options } from 'easymde';
import EasyMDE from 'easymde';
import { useToast } from '@/hooks/use-toast';
import { Command, CommandGroup, CommandItem, CommandInput, CommandList } from './ui/command';
import { Popover, PopoverContent, PopoverTrigger } from './ui/popover';
import { v4 as uuidv4 } from 'uuid';
import { AttachmentPill } from './AttachmentPill'; // Import AttachmentPill

interface NewMessageDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

export const NewMessageDialog = ({ open, onOpenChange }: NewMessageDialogProps) => {
  const { workspace, sendMessage, setCurrentChannel, addDirectMessage } = useApp();
  const { toast } = useToast();
  const [selectedChannelId, setSelectedChannelId] = useState<string | null>(null);
  const [selectedUserId, setSelectedUserId] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [messageContent, setMessageContent] = useState('');
  const [showTopicSelector, setShowTopicSelector] = useState(false);
  const [topicTitle, setTopicTitle] = useState('');
  const [selectedTopicId, setSelectedTopicId] = useState<string | null>(null);
  const [showRecipientDropdown, setShowRecipientDropdown] = useState(false);
  const [isKeyboardMode, setIsKeyboardMode] = useState(false);
  const [pendingAttachments, setPendingAttachments] = useState<LocalAttachment[]>([]);
  
  const toInputRef = useRef<HTMLInputElement>(null);
  const messageEditorRef = useRef<EasyMDE | null>(null);
  const topicListRef = useRef<HTMLDivElement>(null);
  const addTopicButtonRef = useRef<HTMLButtonElement>(null);
  const dropZoneRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const allChannels = workspace.sections.flatMap(section => section.channels);
  const allUsers = workspace.users.filter(user => user.id !== workspace.currentUserId);
  
  useEffect(() => {
    if (open) {
      setSelectedChannelId(null);
      setSelectedUserId(null);
      setSearchQuery('');
      setMessageContent('');
      setTopicTitle('');
      setSelectedTopicId(null);
      setShowTopicSelector(false);
      setShowRecipientDropdown(false);
      setPendingAttachments([]);
      setTimeout(() => toInputRef.current?.focus(), 100);
    }
  }, [open]);
  
  useEffect(() => {
    if (!showTopicSelector) return;
    const handleClickOutside = (event: MouseEvent) => {
      if (topicListRef.current && !topicListRef.current.contains(event.target as Node) && addTopicButtonRef.current && !addTopicButtonRef.current.contains(event.target as Node)) {
        setShowTopicSelector(false);
      }
    };
    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, [showTopicSelector]);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setShowRecipientDropdown(false);
    }
  }, [searchQuery]);
  
  const getChannelTopics = (): ChannelTopic[] => {
    if (!selectedChannelId) return [];
    const channel = allChannels.find(c => c.id === selectedChannelId);
    return channel?.channelTopics || [];
  };
  
  const handleRecipientSelect = (type: 'channel' | 'user', id: string) => {
    if (type === 'channel') { setSelectedChannelId(id); setSelectedUserId(null); } 
    else { setSelectedUserId(id); setSelectedChannelId(null); }
    setShowRecipientDropdown(false);
    setSearchQuery('');
    setTimeout(() => messageEditorRef.current?.codemirror.focus(), 100);
  };
  
  const handleTopicSelect = (topicId: string | 'new') => {
    if (topicId === 'new') { setTopicTitle('New topic'); setSelectedTopicId(null); } 
    else {
      const topic = getChannelTopics().find(t => t.id === topicId);
      if (topic) { setTopicTitle(topic.title); setSelectedTopicId(topicId); }
    }
    setShowTopicSelector(false);
    setTimeout(() => messageEditorRef.current?.codemirror.focus(), 100);
  };
  
  const getFilteredRecipients = () => {
    const trimmedSearchQuery = searchQuery.trim();

    if (trimmedSearchQuery === '#') {
      return { channels: allChannels, users: [], topMatchId: allChannels[0]?.id };
    }
    if (trimmedSearchQuery === '@') {
      return { channels: [], users: allUsers, topMatchId: allUsers[0]?.id };
    }

    const queryForFiltering = trimmedSearchQuery.toLowerCase().replace(/^[@#]/, '');

    if (queryForFiltering.length < 1) {
      // This handles cases like "  ", "# ", "@ " or if initial searchQuery was ""
      return { channels: [], users: [], topMatchId: undefined };
    }

    const scoreMatch = (name: string, q: string): number => {
      const lowerName = name.toLowerCase();
      const lowerQ = q.toLowerCase();
      if (lowerName === lowerQ) return 100;
      if (lowerName.startsWith(lowerQ)) return 80;
      if (new RegExp(`\\b${lowerQ}\\b`, 'i').test(lowerName)) return 60; // Case-insensitive word boundary
      if (lowerName.includes(lowerQ)) return 40;
      return 0;
    };
    
    let channelsResult: Channel[] = [];
    let usersResult: User[] = [];
    let topMatchId: string | undefined = undefined;

    // Determine what to filter based on the original trimmed query's prefix
    const searchChannels = trimmedSearchQuery.startsWith('#') || !trimmedSearchQuery.startsWith('@');
    const searchUsers = trimmedSearchQuery.startsWith('@') || !trimmedSearchQuery.startsWith('#');

    let scoredChannels: { channel: Channel, score: number }[] = [];
    if (searchChannels) {
      scoredChannels = allChannels
        .map(c => ({ channel: c, score: scoreMatch(c.name, queryForFiltering) }))
        .filter(i => i.score > 0)
        .sort((a, b) => b.score - a.score);
      channelsResult = scoredChannels.map(i => i.channel);
    }

    let scoredUsers: { user: User, score: number }[] = [];
    if (searchUsers) {
      scoredUsers = allUsers
        .map(u => ({ user: u, score: scoreMatch(u.name, queryForFiltering) }))
        .filter(i => i.score > 0)
        .sort((a, b) => b.score - a.score);
      usersResult = scoredUsers.map(i => i.user);
    }

    if (trimmedSearchQuery.startsWith('#')) {
      if (channelsResult.length > 0) topMatchId = channelsResult[0].id;
      return { channels: channelsResult, users: [], topMatchId };
    }
    
    if (trimmedSearchQuery.startsWith('@')) {
      if (usersResult.length > 0) topMatchId = usersResult[0].id;
      return { channels: [], users: usersResult, topMatchId };
    }

    // No prefix, or prefix was stripped and queryForFiltering is not empty
    // Determine topMatchId based on scores
    const topChannelScore = scoredChannels.length > 0 ? scoredChannels[0].score : 0;
    const topUserScore = scoredUsers.length > 0 ? scoredUsers[0].score : 0;

    if (topChannelScore > 0 || topUserScore > 0) {
      if (topChannelScore >= topUserScore) {
        if (channelsResult.length > 0) topMatchId = channelsResult[0].id;
      } else {
        if (usersResult.length > 0) topMatchId = usersResult[0].id;
      }
    }
    
    return { channels: channelsResult, users: usersResult, topMatchId };
  };
  
  const { channels: filteredChannels, users: filteredUsers, topMatchId } = getFilteredRecipients();
  
  const getSelectedRecipient = () => {
    if (selectedChannelId) { const c = allChannels.find(ch => ch.id === selectedChannelId); return { type: 'channel' as const, name: c?.name || '', isPrivate: c?.isPrivate }; } 
    if (selectedUserId) { const u = allUsers.find(usr => usr.id === selectedUserId); return { type: 'user' as const, name: u?.name || '', avatar: u?.avatar }; }
    return null;
  };
  
  const messageContentRef = useRef(messageContent);
  const getSelectedRecipientRef = useRef(getSelectedRecipient);
  useEffect(() => { messageContentRef.current = messageContent; }, [messageContent]);
  useEffect(() => { getSelectedRecipientRef.current = getSelectedRecipient; }, [selectedChannelId, selectedUserId, allChannels, allUsers]);
  
  const mdeOptions: Options = useMemo(() => ({
    autofocus: false, spellChecker: false, placeholder: "Type your message here...", status: false,
    toolbar: ['bold', 'italic', 'strikethrough', '|', 'heading', 'heading-smaller', 'heading-bigger', '|', 'code', 'quote', '|', 'unordered-list', 'ordered-list', '|', 'link', 'image', 'table', '|', 'horizontal-rule', 'preview', 'side-by-side', 'fullscreen'] as const,
    lineWrapping: true,
  }), []);

  const addFileAsAttachment = async (file: File) => {
    const localId = uuidv4();
    const newAttachmentBase: Omit<LocalAttachment, 'dataUrl' | 'textContent'> = {
      id: localId, name: file.name, type: file.type, size: file.size, fileObject: file,
    };
    if (file.type.startsWith('image/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPendingAttachments(prev => [...prev, { ...newAttachmentBase, dataUrl: e.target?.result as string }]);
      reader.readAsDataURL(file);
    } else if (file.type.startsWith('text/')) {
      const reader = new FileReader();
      reader.onload = (e) => setPendingAttachments(prev => [...prev, { ...newAttachmentBase, textContent: e.target?.result as string }]);
      reader.readAsText(file);
    } else {
      console.warn(`File type ${file.type} not directly supported for inline content.`);
      setPendingAttachments(prev => [...prev, { ...newAttachmentBase }]);
    }
  };

  const removeAttachment = (attachmentId: string) => {
    setPendingAttachments(prev => prev.filter(att => att.id !== attachmentId));
  };

  const handlePaste = (_instance: any, event: ClipboardEvent) => {
    if (event.clipboardData) {
      for (let i = 0; i < event.clipboardData.items.length; i++) {
        const item = event.clipboardData.items[i];
        if (item.kind === 'file') {
          const file = item.getAsFile();
          if (file) { event.preventDefault(); addFileAsAttachment(file); }
        }
      }
    }
  };

  const handleDragOver = (event: React.DragEvent<HTMLDivElement>) => event.preventDefault();

  const handleDrop = (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const recipientSelected = selectedChannelId || selectedUserId;
    if (!recipientSelected) {
        toast({ title: "Select Recipient", description: "Please select a channel or user before attaching files.", variant: "default" });
        return;
    }
    if (event.dataTransfer.files && event.dataTransfer.files.length > 0) {
      Array.from(event.dataTransfer.files).forEach(file => addFileAsAttachment(file));
      event.dataTransfer.clearData();
    }
  };

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      Array.from(event.target.files).forEach(file => {
        addFileAsAttachment(file);
      });
    }
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };
  
  const handleSendMessage = async () => {
    if (!messageContentRef.current.trim() && pendingAttachments.length === 0) {
      toast({ title: "Empty message", description: "Please type a message or add an attachment", variant: "destructive" }); return;
    }
    if (selectedChannelId) {
      const topicIdToSend = selectedTopicId ? selectedTopicId : undefined;
      await sendMessage(messageContentRef.current, selectedChannelId, undefined, undefined, topicIdToSend, pendingAttachments);
      toast({ title: "Message sent", description: `Sent to channel ${topicTitle ? `with topic: ${topicTitle}` : ''}` });
      setCurrentChannel(selectedChannelId); onOpenChange(false);
    } else if (selectedUserId) {
      const dmSessionId = await addDirectMessage(selectedUserId);
      if (dmSessionId) {
        await sendMessage(messageContentRef.current, undefined, dmSessionId, undefined, undefined, pendingAttachments);
        toast({ title: "Message sent", description: "Direct message sent" }); onOpenChange(false);
      } else {
        toast({ title: "Failed to send message", description: "Could not establish DM session.", variant: "destructive" });
      }
    }
  };
  
  const handleTopicListKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    if (!showTopicSelector) return; const items = Array.from(topicListRef.current?.querySelectorAll('button, [role="button"]') || []) as HTMLElement[]; if (items.length === 0) return; const currentIndex = items.findIndex(item => item === document.activeElement);
    if (e.key === 'ArrowDown') { e.preventDefault(); const nextIndex = currentIndex === -1 ? 0 : (currentIndex + 1) % items.length; items[nextIndex]?.focus(); } 
    else if (e.key === 'ArrowUp') { e.preventDefault(); const prevIndex = currentIndex === -1 ? items.length -1 : (currentIndex - 1 + items.length) % items.length; items[prevIndex]?.focus(); } 
    else if (e.key === 'Escape') { e.preventDefault(); setShowTopicSelector(false); if (addTopicButtonRef.current) addTopicButtonRef.current.focus(); } 
    else if (e.key === 'Enter' && currentIndex >= 0) { e.preventDefault(); items[currentIndex].click(); }
  };
  
  const recipient = getSelectedRecipient();
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => { const value = e.target.value; setSearchQuery(value); setShowRecipientDropdown(!!value.trim()); };
  const handleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Tab') setIsKeyboardMode(true);
    if (e.key === '#' && !searchQuery.startsWith('#')) { setSearchQuery('#'); setShowRecipientDropdown(true); e.preventDefault(); return; } 
    else if (e.key === '@' && !searchQuery.startsWith('@')) { setSearchQuery('@'); setShowRecipientDropdown(true); e.preventDefault(); return; }
    if (showRecipientDropdown && e.key === 'Tab') {
      const cmdList = document.querySelector('[cmdk-list]'); if (!cmdList) return; let itemToSelect: HTMLElement | null = cmdList.querySelector('[data-selected="true"]') as HTMLElement;
      if (!itemToSelect && searchQuery.length > 1) { const visItems = Array.from(cmdList.querySelectorAll('div[cmdk-item=""]')).filter(el => window.getComputedStyle(el).display !== 'none') as HTMLElement[]; if (visItems.length === 1) itemToSelect = visItems[0]; }
      if (itemToSelect) { e.preventDefault(); const val = itemToSelect.dataset.value; const type = itemToSelect.dataset.type as 'channel' | 'user' | undefined; if (val && type) handleRecipientSelect(type, val); } else { setShowRecipientDropdown(false); }
    }
    if (showRecipientDropdown && e.key === 'Enter') {
      e.preventDefault(); const cmdList = document.querySelector('[cmdk-list]'); if (!cmdList) return; let itemToSelect: HTMLElement | null = cmdList.querySelector('[data-selected="true"]') as HTMLElement;
      if (!itemToSelect && searchQuery.length > 1) {
        const visItems = Array.from(cmdList.querySelectorAll('div[cmdk-item=""]')).filter(el => window.getComputedStyle(el).display !== 'none') as HTMLElement[];
        if (visItems.length === 1) itemToSelect = visItems[0];
        else if (visItems.length > 1) { const q = searchQuery.replace(/^[@#]/, '').toLowerCase(); const exactMatch = visItems.find(item => { const iTxt = item.textContent?.toLowerCase() || ''; return iTxt.includes(q) && (iTxt.includes(` ${q}`) || iTxt.includes(`${q} `) || iTxt === q); }); if (exactMatch) itemToSelect = exactMatch; }
      }
      if (itemToSelect) { const val = itemToSelect.dataset.value; const type = itemToSelect.dataset.type as 'channel' | 'user' | undefined; if (val && type) handleRecipientSelect(type, val); }
    }
  };
  useEffect(() => { const handleMouseMove = () => setIsKeyboardMode(false); window.addEventListener('mousemove', handleMouseMove); return () => window.removeEventListener('mousemove', handleMouseMove); }, []);
  
  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent 
        ref={dropZoneRef}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        className="w-[90%] max-w-[90%] h-[90vh] flex flex-col"
      >
        <DialogHeader>
          <DialogTitle>New message</DialogTitle>
        </DialogHeader>
        
        <div className="mt-2 flex flex-col flex-grow min-h-0">
          <div className="mb-4">
            <div className="flex items-center border-b pb-2">
              <span className="text-sm text-gray-500 mr-2">To:</span>
              {recipient ? (
                <div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md">
                  {recipient.type === 'channel' ? (<>{recipient.isPrivate ? <Lock size={14} /> : <Hash size={14} />}<span>{recipient.name}</span></>) : (<div className="flex items-center gap-1"><Avatar className="h-5 w-5"><AvatarImage src={recipient.avatar} /><AvatarFallback><AtSign size={12} /></AvatarFallback></Avatar><span>{recipient.name}</span></div>)}
                  <Button variant="ghost" size="icon" className="h-5 w-5 ml-1" onClick={() => { setSelectedChannelId(null); setSelectedUserId(null); setSearchQuery(''); setTimeout(() => toInputRef.current?.focus(), 10); }}><X size={12} /></Button>
                </div>
              ) : (<Input ref={toInputRef} className="border-0 focus-visible:ring-0 px-0 text-sm" placeholder="#channel, @somebody, or <EMAIL>" value={searchQuery} onChange={handleInputChange} onKeyDown={handleInputKeyDown} />)}
            </div>
            {searchQuery.trim().length > 0 && !recipient && (
              <div className="relative"><div className="absolute z-50 top-0 left-0 w-full mt-1 bg-white dark:bg-gray-800 border rounded-md shadow-md">
                <Command className="border-0 p-0" key={`search-${searchQuery}`} filter={() => 1} value={topMatchId}>
                  <CommandInput value={searchQuery} onValueChange={setSearchQuery} className="hidden" />
                  <CommandList onKeyDown={(e) => { if (searchQuery.trim().length === 0) return; if (e.key === 'Escape') { setSearchQuery(''); toInputRef.current?.focus(); }}}>
                    {filteredChannels.length > 0 && (<CommandGroup heading="Channels">{filteredChannels.map((c, i) => (<CommandItem key={c.id} value={c.id} onSelect={() => handleRecipientSelect('channel', c.id)} data-value={c.id} data-type="channel" className="flex items-center cursor-pointer" data-selected={i === 0 && (!filteredUsers.length || searchQuery.trim().startsWith('#')) ? "true" : undefined}>{c.isPrivate ? <Lock size={14} className="mr-2" /> : <Hash size={14} className="mr-2" />}<span>{c.name}</span></CommandItem>))}</CommandGroup>)}
                    {filteredUsers.length > 0 && (<CommandGroup heading="People">{filteredUsers.map((u, i) => (<CommandItem key={u.id} value={u.id} onSelect={() => handleRecipientSelect('user', u.id)} data-value={u.id} data-type="user" className="flex items-center cursor-pointer" data-selected={i === 0 && (!filteredChannels.length || searchQuery.trim().startsWith('@')) ? "true" : undefined}><Avatar className="h-6 w-6 mr-2"><AvatarImage src={u.avatar} /><AvatarFallback>{u.name.charAt(0)}</AvatarFallback></Avatar><span>{u.name}</span></CommandItem>))}</CommandGroup>)}
                    {filteredChannels.length === 0 && filteredUsers.length === 0 && (<div className="py-6 text-center text-gray-500"><Search className="mx-auto h-6 w-6" /><p className="mt-2">No results found</p></div>)}
                  </CommandList>
                </Command>
              </div></div>
            )}
          </div>
          {selectedChannelId && (
            <div className="mb-4 relative">
              <div className="flex items-center border-b pb-2">
                <span className="text-sm text-gray-500 mr-2">Topic:</span>
                {topicTitle ? (<div className="flex items-center gap-1 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded-md"><span className="text-sm">{topicTitle}</span><Button variant="ghost" size="icon" className="h-5 w-5 ml-1" onClick={() => { setTopicTitle(''); setSelectedTopicId(null); setTimeout(() => messageEditorRef.current?.codemirror.focus(), 50); }} aria-label="Clear topic selection"><X size={12} /></Button></div>) : (
                  <div className="flex items-center">
                    <Button variant="ghost" ref={addTopicButtonRef} size="sm" className="p-0 h-7 text-blue-500 hover:text-blue-600 dark:text-blue-400 dark:hover:text-blue-300 hover:bg-transparent" onClick={() => { const newShowState = !showTopicSelector; setShowTopicSelector(newShowState); if (newShowState) { setTimeout(() => { const firstItem = topicListRef.current?.querySelector('button, [role="button"]'); if (firstItem instanceof HTMLElement) firstItem.focus(); }, 50); }}} aria-haspopup="listbox" aria-expanded={showTopicSelector}><Plus size={14} className="mr-1" /> Associate with Topic</Button>
                    {isKeyboardMode && (<span className="ml-2 text-xs text-gray-400 dark:text-gray-500" title="Press Ctrl+Shift+T">Ctrl+Shift+T</span>)}
                  </div>
                )}
              </div>
              {showTopicSelector && (<div className="absolute z-50 top-full left-0 w-full mt-1 bg-white dark:bg-gray-800 border dark:border-gray-700 rounded-md shadow-lg"><ScrollArea className="max-h-48"><div className="p-1" ref={topicListRef} onKeyDown={handleTopicListKeyDown} role="listbox" tabIndex={-1}>
                <Button variant="ghost" size="sm" className="w-full justify-start text-blue-500 dark:text-blue-400 hover:bg-gray-100 dark:hover:bg-gray-700" onClick={() => handleTopicSelect('new')} role="option"><Plus size={14} className="mr-2" /> Create new topic</Button>
                {getChannelTopics().filter(t => !t.is_archived).length > 0 && (<><Separator className="my-1 dark:bg-gray-700" /><div className="text-xs text-gray-500 dark:text-gray-400 px-2 py-1">Existing topics</div>{getChannelTopics().filter(t => !t.is_archived).map(topic => (<Button key={topic.id} variant="ghost" size="sm" className="w-full justify-start hover:bg-gray-100 dark:hover:bg-gray-700" onClick={() => handleTopicSelect(topic.id)} role="option" aria-selected={selectedTopicId === topic.id}>{topic.title}</Button>))}</>)}
                {getChannelTopics().filter(t => !t.is_archived).length === 0 && (<div className="text-xs text-gray-400 dark:text-gray-500 px-2 py-2 text-center">No topics.</div>)}
              </div></ScrollArea></div>)}
            </div>
          )}
          <div className="border rounded-md overflow-hidden flex-grow flex flex-col dark:border-gray-700 min-h-0">
            <SimpleMDEEditor id="message-editor" value={messageContent} onChange={setMessageContent} options={mdeOptions}
              getMdeInstance={(instance: EasyMDE) => {
                if (instance) {
                  messageEditorRef.current = instance;
                  if ((instance as any)._ctrlEnterHandler) instance.codemirror.off("keydown", (instance as any)._ctrlEnterHandler);
                  const ctrlEnterHandler = (cm: any, event: KeyboardEvent) => {
                    if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') { event.preventDefault(); event.stopPropagation(); if ((messageContentRef.current.trim() || pendingAttachments.length > 0) && getSelectedRecipientRef.current()) handleSendMessage(); }
                    if (event.ctrlKey && event.shiftKey && event.key === 'T' && selectedChannelId) { event.preventDefault(); event.stopPropagation(); if (addTopicButtonRef.current) addTopicButtonRef.current.click(); }
                    if (event.key === 'Tab') setIsKeyboardMode(true);
                  };
                  instance.codemirror.on("keydown", ctrlEnterHandler);
                  (instance as any)._ctrlEnterHandler = ctrlEnterHandler;
                  instance.codemirror.on("focus", () => setShowTopicSelector(false));
                  instance.codemirror.on("paste", (_cm: any, event: ClipboardEvent) => handlePaste(instance, event));
                }
              }}
            />
          </div>
          <div className="attachment-pills-area px-3 py-2 border-t border-b border-[var(--app-border)] dark:border-gray-700 mt-2 min-h-[40px] flex flex-wrap gap-2">
            {pendingAttachments.length === 0 && <div className="text-sm text-gray-500 dark:text-gray-400 py-1">No attachments yet. Drag & drop or use the '+' button.</div>}
            {pendingAttachments.map(att => (
              <AttachmentPill key={att.id} attachment={att} onRemove={removeAttachment} />
            ))}
          </div>
          <div className="mt-auto pt-4 flex items-center">
            <input
              type="file"
              multiple
              ref={fileInputRef}
              onChange={handleFileSelect}
              className="hidden"
              accept="image/*,text/plain,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.zip,.rar" // Expanded acceptable file types
            />
            <Button 
              variant="outline" 
              size="icon" 
              className="mr-2" 
              onClick={() => fileInputRef.current?.click()}
              disabled={!recipient} 
              aria-label="Attach file"
            >
              <Paperclip size={18} />
            </Button>
            <div className="flex-grow" />
            <Button onClick={handleSendMessage} disabled={!recipient || (!messageContent.trim() && pendingAttachments.length === 0)}>Send message{isKeyboardMode && <span className="ml-2 text-xs opacity-75">Ctrl+Enter</span>}</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};
